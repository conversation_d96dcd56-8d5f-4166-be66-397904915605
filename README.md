﻿# NextJS-SMS-Template

NextJS-SMS-Template is a platform designed to assist school administration, teachers, and students in managing information and educational processes efficiently. Here are the key features of this SMS template:


## Admin Features
- **Student Management**: Add, view, edit, and delete student records. Manage classes.
- **Teacher Management**: Add, view, edit, and delete teacher records. Manage schedules.
- **Class Management**: Create, view, edit, and delete class information. Manage schedules and course data.
- **Schedule Management**: Create and manage class schedules, teacher schedules, and student schedules. Generate schedule reports.
- **Assignment Management**: Create, edit and delete assignments.
## Teacher Features
- **Teaching Schedule**: View teaching schedule.
- **Assignment Management**: Create, edit and delete assignments.

## Student Features
- **Simple Page**
    
## Technologies Used
- **Frontend**: React.js, Next.js
- **Backend**: Prisma, PostgreSQL
- **Authentication**: NextAuthV5
- **Storage**: Firebase Storage, Supabase

## Getting Started
1. Clone this repository: `git clone https://github.com/zxmodren/NextJS-SMS-Template.git`
2. Install dependencies: `npm install`
3. Configure the database and environment variables.
4. Generate prisma : `npx prisma generate`
5. Run the development server: `npm run dev`
6. Access the application at `http://localhost:3000`

## License
This project is licensed under the MIT LICENSE - see the LICENSE.md file for details.


## Developer Message 
Hi everyone, this is my first and probably last Next.js project due to my laptop's very low end specifications (Super Potato). So, I won't be able to continue this project for an indefinite time. Thank you all for visiting, have a good day!


## Footage
[![Picture1](https://i.ibb.co/ZNJJfMp/Picture1.png)](https://imgbb.com/)


## DONATE
<a href="https://www.buymeacoffee.com/aryaferdya9"><img src="https://img.buymeacoffee.com/button-api/?text=Buy me a laptop&emoji=💻&slug=aryaferdya9&button_colour=FFDD00&font_colour=000000&font_family=Cookie&outline_colour=000000&coffee_colour=ffffff" /></a>
