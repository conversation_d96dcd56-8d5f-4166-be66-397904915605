{"name": "nextj-sms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^1.0.12", "@hookform/resolvers": "^3.3.3", "@prisma/client": "^5.11.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@react-email/components": "^0.0.17", "axios": "^1.6.8", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.0.0", "debounce": "^2.0.0", "firebase": "^10.9.0", "framer-motion": "^11.1.7", "mini-svg-data-uri": "^1.4.4", "next": "14.0.4", "next-auth": "^5.0.0-beta.4", "nextjs-toploader": "^1.6.11", "react": "^18", "react-dom": "^18", "react-email": "^2.1.2", "react-hook-form": "^7.49.2", "react-icons": "^4.12.0", "react-spinners": "^0.13.8", "resend": "^2.1.0", "sonner": "^1.3.1", "swr": "^2.2.5", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.0", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.7", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "postcss": "^8", "prettier": "^3.2.5", "prisma": "^5.11.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}