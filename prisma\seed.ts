import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'School Administrator',
      password: hashedPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
      gender: 'UNKNOW',
      emailVerified: new Date(),
    },
  })

  // Create a teacher user
  const teacherPassword = await bcrypt.hash('teacher123', 12)
  
  const teacher = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'John <PERSON>',
      password: teacherPassword,
      role: 'TEACHER',
      status: 'ACTIVE',
      gender: 'MALE',
      emailVerified: new Date(),
    },
  })

  // Create teacher record
  await prisma.teachers.upsert({
    where: { userId: teacher.id },
    update: {},
    create: {
      userId: teacher.id,
      name: teacher.name,
    },
  })

  // Create a student user
  const studentPassword = await bcrypt.hash('student123', 12)
  
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Jane Student',
      password: studentPassword,
      role: 'STUDENT',
      status: 'ACTIVE',
      gender: 'FEMALE',
      emailVerified: new Date(),
    },
  })

  // Create student record
  await prisma.students.upsert({
    where: { userId: student.id },
    update: {},
    create: {
      userId: student.id,
      name: student.name,
    },
  })

  // Create a classroom
  const classroom = await prisma.classrooms.upsert({
    where: { id: 'classroom-1' },
    update: {},
    create: {
      id: 'classroom-1',
      name: 'Class 10A',
      cap: '30',
    },
  })

  // Create a lesson
  const lesson = await prisma.lessons.upsert({
    where: { id: 'lesson-1' },
    update: {},
    create: {
      id: 'lesson-1',
      name: 'Mathematics',
      teacherId: teacher.id,
      cat: 'SCIENCE',
    },
  })

  console.log('Database seeded successfully!')
  console.log('Admin user: <EMAIL> / admin123')
  console.log('Teacher user: <EMAIL> / teacher123')
  console.log('Student user: <EMAIL> / student123')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
